import { useState, useCallback, useEffect } from "react";
import { useGetTemplateNodesQuery } from "@/api/templatesApiSlice";
import type {
  PrimaryFolder,
  SecondaryFolder,
  TreeValidation,
} from "./useCreateTemplatePopup";

interface UseTemplateTreeViewWithDataProps {
  templateId?: number;
  isEditMode?: boolean;
}

export function useTemplateTreeViewWithData({
  templateId,
  isEditMode = false,
}: UseTemplateTreeViewWithDataProps) {
  const generateUniqueId = useCallback(() => {
    const timestamp = Date.now();
    const randomPart = Math.random().toString(36).slice(2, 10);
    return `folder-${timestamp}-${randomPart}`;
  }, []);

  // Fetch template nodes when in edit mode
  const { data: templateNodes, isLoading } = useGetTemplateNodesQuery(
    templateId!,
    {
      skip: !isEditMode || !templateId,
    }
  );

  const [primaryFolders, setPrimaryFolders] = useState<PrimaryFolder[]>([]);
  const [newFolderIds, setNewFolderIds] = useState<Set<string>>(new Set());

  const [validation, setValidation] = useState<TreeValidation>({
    duplicateNameError: "",
    deleteLastSecondaryError: "",
  });

  // Initialize folders based on mode
  useEffect(() => {
    if (isEditMode && templateNodes && templateNodes.length > 0) {
      // Convert API data to internal format
      const convertedFolders: PrimaryFolder[] = templateNodes.map((node) => ({
        id: `existing-${node.id}`,
        name: node.name,
        isEditing: false,
        expanded: true,
        secondaryFolders: node.childNodes.map((child) => ({
          id: `existing-${child.id}`,
          name: child.name,
          isEditing: false,
        })),
      }));
      setPrimaryFolders(convertedFolders);
    } else if (!isEditMode) {
      // Default structure for create mode
      const defaultFolder: PrimaryFolder = {
        id: generateUniqueId(),
        name: "Primary Folder",
        isEditing: false,
        expanded: true,
        secondaryFolders: [
          {
            id: generateUniqueId(),
            name: "Secondary Folder",
            isEditing: false,
          },
        ],
      };
      setPrimaryFolders([defaultFolder]);
      setNewFolderIds(new Set([defaultFolder.id, defaultFolder.secondaryFolders[0].id]));
    }
  }, [isEditMode, templateNodes, generateUniqueId]);

  const clearValidationErrors = useCallback(() => {
    setValidation({
      duplicateNameError: "",
      deleteLastSecondaryError: "",
    });
  }, []);

  const isExistingFolder = useCallback((folderId: string) => {
    return folderId.startsWith("existing-");
  }, []);

  const canDeleteFolder = useCallback((folderId: string) => {
    // In edit mode, only allow deletion of newly added folders
    if (isEditMode) {
      return newFolderIds.has(folderId);
    }
    // In create mode, allow deletion of all folders (with business rules)
    return true;
  }, [isEditMode, newFolderIds]);

  const generateUniqueName = useCallback((baseName: string, existingNames: string[]): string => {
    let name = baseName;
    let counter = 1;

    while (existingNames.includes(name.toLowerCase())) {
      name = `${baseName}(${counter})`;
      counter++;
    }

    return name;
  }, []);

  const addPrimaryFolder = useCallback(() => {
    const newId = generateUniqueId();
    const secondaryId = generateUniqueId();

    // Get all existing folder names for uniqueness check
    const existingNames: string[] = [];
    primaryFolders.forEach((pf) => {
      existingNames.push(pf.name.toLowerCase());
      pf.secondaryFolders.forEach((sf) => {
        existingNames.push(sf.name.toLowerCase());
      });
    });

    const uniquePrimaryName = generateUniqueName("Primary Folder", existingNames);
    const uniqueSecondaryName = generateUniqueName("Secondary Folder", [...existingNames, uniquePrimaryName.toLowerCase()]);

    const newFolder: PrimaryFolder = {
      id: newId,
      name: uniquePrimaryName,
      isEditing: false, // Changed to false to show as added entity, not in edit mode
      expanded: true,
      secondaryFolders: [
        {
          id: secondaryId,
          name: uniqueSecondaryName,
          isEditing: false, // Changed to false to show as added entity, not in edit mode
        }
      ],
    };

    setPrimaryFolders((prev) => [...prev, newFolder]);
    setNewFolderIds((prev) => new Set([...prev, newId, secondaryId]));
    clearValidationErrors();
  }, [generateUniqueId, generateUniqueName, primaryFolders, clearValidationErrors]);

  const addSecondaryFolder = useCallback(
    (primaryFolderId: string) => {
      const newId = generateUniqueId();

      // Get all existing folder names for uniqueness check
      const existingNames: string[] = [];
      primaryFolders.forEach((pf) => {
        existingNames.push(pf.name.toLowerCase());
        pf.secondaryFolders.forEach((sf) => {
          existingNames.push(sf.name.toLowerCase());
        });
      });

      const uniqueSecondaryName = generateUniqueName("Secondary Folder", existingNames);

      const newSecondaryFolder: SecondaryFolder = {
        id: newId,
        name: uniqueSecondaryName,
        isEditing: false, // Changed to false to show as added entity, not in edit mode
      };

      setPrimaryFolders((prev) =>
        prev.map((folder) =>
          folder.id === primaryFolderId
            ? {
                ...folder,
                secondaryFolders: [...folder.secondaryFolders, newSecondaryFolder],
              }
            : folder
        )
      );
      setNewFolderIds((prev) => new Set([...prev, newId]));
      clearValidationErrors();
    },
    [generateUniqueId, generateUniqueName, primaryFolders, clearValidationErrors]
  );

  const editFolder = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      setPrimaryFolders((prev) =>
        prev.map((folder) => {
          if (!isSecondary && folder.id === folderId) {
            return { ...folder, isEditing: true };
          }
          if (isSecondary && folder.id === primaryFolderId) {
            return {
              ...folder,
              secondaryFolders: folder.secondaryFolders.map((sf) =>
                sf.id === folderId ? { ...sf, isEditing: true } : sf
              ),
            };
          }
          return folder;
        })
      );
      clearValidationErrors();
    },
    [clearValidationErrors]
  );

  const saveFolder = useCallback(
    (
      folderId: string,
      newName: string,
      isSecondary: boolean,
      primaryFolderId?: string
    ): boolean => {
      if (!newName.trim()) {
        return false;
      }

      // Check for duplicate names
      const allNames: string[] = [];
      primaryFolders.forEach((pf) => {
        if (pf.id !== folderId) {
          allNames.push(pf.name.toLowerCase());
        }
        pf.secondaryFolders.forEach((sf) => {
          if (sf.id !== folderId) {
            allNames.push(sf.name.toLowerCase());
          }
        });
      });

      if (allNames.includes(newName.toLowerCase())) {
        setValidation((prev) => ({
          ...prev,
          duplicateNameError: "Folder name already exists",
        }));
        return false;
      }

      setPrimaryFolders((prev) =>
        prev.map((folder) => {
          if (!isSecondary && folder.id === folderId) {
            return { ...folder, name: newName, isEditing: false };
          }
          if (isSecondary && folder.id === primaryFolderId) {
            return {
              ...folder,
              secondaryFolders: folder.secondaryFolders.map((sf) =>
                sf.id === folderId
                  ? { ...sf, name: newName, isEditing: false }
                  : sf
              ),
            };
          }
          return folder;
        })
      );

      clearValidationErrors();
      return true;
    },
    [primaryFolders, clearValidationErrors]
  );

  const cancelEdit = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      setPrimaryFolders((prev) =>
        prev.map((folder) => {
          if (!isSecondary && folder.id === folderId) {
            return { ...folder, isEditing: false };
          }
          if (isSecondary && folder.id === primaryFolderId) {
            return {
              ...folder,
              secondaryFolders: folder.secondaryFolders.map((sf) =>
                sf.id === folderId ? { ...sf, isEditing: false } : sf
              ),
            };
          }
          return folder;
        })
      );
      clearValidationErrors();
    },
    [clearValidationErrors]
  );

  const deleteFolder = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      if (!canDeleteFolder(folderId)) {
        return;
      }

      if (isSecondary) {
        const primaryFolder = primaryFolders.find((pf) => pf.id === primaryFolderId);
        if (primaryFolder && primaryFolder.secondaryFolders.length === 1) {
          setValidation((prev) => ({
            ...prev,
            deleteLastSecondaryError:
              "Cannot delete the last secondary folder. Each primary folder must have at least one secondary folder.",
          }));
          return;
        }

        setPrimaryFolders((prev) =>
          prev.map((folder) =>
            folder.id === primaryFolderId
              ? {
                  ...folder,
                  secondaryFolders: folder.secondaryFolders.filter(
                    (sf) => sf.id !== folderId
                  ),
                }
              : folder
          )
        );
      } else {
        if (primaryFolders.length === 1) {
          setValidation((prev) => ({
            ...prev,
            deleteLastSecondaryError:
              "Cannot delete the last primary folder. At least one primary folder is required.",
          }));
          return;
        }

        setPrimaryFolders((prev) => prev.filter((folder) => folder.id !== folderId));
      }

      setNewFolderIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });

      clearValidationErrors();
    },
    [primaryFolders, canDeleteFolder, clearValidationErrors]
  );

  const toggleExpand = useCallback((primaryFolderId: string) => {
    setPrimaryFolders((prev) =>
      prev.map((folder) =>
        folder.id === primaryFolderId
          ? { ...folder, expanded: !folder.expanded }
          : folder
      )
    );
  }, []);

  return {
    primaryFolders,
    validation,
    isLoading,
    isExistingFolder,
    canDeleteFolder,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  };
}
