import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { TypeGridColumn } from "@/types/column";
import { mockTemplateColumns, mockTemplatesData } from "./mocks/templatesMock";
import type { TemplateListResponse } from "@/types/templates";
import {
  filterBy,
  orderBy,
  type CompositeFilterDescriptor,
  type SortDescriptor,
} from "@progress/kendo-data-query";
import config from "@/config";

interface GetTemplateListParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

export interface TemplateNode {
  id: number;
  name: string;
  totalChildNodes: number;
  childNodes: TemplateChildNode[];
}

export interface TemplateChildNode {
  id: number;
  name: string;
}

// Mock data for template nodes
const mockTemplateNodes: TemplateNode[] = [
  {
    id: 1,
    name: "Node 1",
    totalChildNodes: 1,
    childNodes: [
      { id: 2, name: "Sub Node 1" }
    ]
  },
  {
    id: 3,
    name: "Node 2",
    totalChildNodes: 1,
    childNodes: [
      { id: 4, name: "Sub Node 2" }
    ]
  }
];

export const templatesApiSlice = createApi({
  reducerPath: "templatesApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getTemplatesGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
        return { data: mockTemplateColumns };
      },
    }),
    getTemplateList: builder.query<TemplateListResponse, GetTemplateListParams>(
      {
        queryFn: async ({ skip, take, filters, sorts }) => {
          await new Promise((resolve) => setTimeout(resolve, 500));

          let currentData = [...mockTemplatesData.records];

          currentData = filterBy(currentData, filters);
          currentData = orderBy(currentData, sorts || []);

          const totalRecordCount = currentData.length;
          const pageNumber = Math.floor(skip / take) + 1;
          const pageCount = Math.ceil(totalRecordCount / take);
          const records = currentData.slice(skip, skip + take);

          return {
            data: {
              records,
              totalRecordCount,
              pageCount,
              pageNumber,
              pageSize: take,
            },
          };
        },
      },
    ),
    getTemplateNodes: builder.query<TemplateNode[], number>({
      queryFn: async (_templateId) => {
        if (config.featureFlags.ui.UPDATE_TEMPLATE) {
          await new Promise((resolve) => setTimeout(resolve, 500));
          return { data: mockTemplateNodes };
        }

        // Real API call would be:
        // return baseQuery({
        //   url: `api/PortalBinderTemplates/${templateId}/nodes`,
        //   method: "GET",
        // });

        // For now, return empty array when feature flag is off
        return { data: [] };
      },
    }),
  }),
});

export const {
  useGetTemplatesGridColumnsQuery,
  useGetTemplateListQuery,
  useGetTemplateNodesQuery
} = templatesApiSlice;
