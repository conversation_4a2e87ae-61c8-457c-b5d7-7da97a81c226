import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { Button } from "@progress/kendo-react-buttons";
import CreateTemplateForm from "./CreateTemplateForm";
import CreateTemplateTreeView from "./CreateTemplateTreeView";
import { useTemplateTreeViewWithData } from "../hooks/useTemplateTreeViewWithData";
import { Loader } from "@progress/kendo-react-indicators";
import type {
  CreateTemplateFormData,
  CreateTemplateValidation,
} from "../hooks/useCreateTemplatePopup";
import type { Template } from "@/types/templates";
import logger from "@/utils/logger";

interface CreateTemplatePopupProps {
  isOpen: boolean;
  isEditMode?: boolean;
  editingTemplate?: Template | null;
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  isSubmitting: boolean;
  onFieldChange: (
    _field: keyof CreateTemplateFormData,
    _value: string | boolean,
  ) => void;
  onCreate: () => void;
  onUpdate?: () => void;
  onCancel: () => void;
}

export default function CreateTemplatePopup({
  isOpen,
  isEditMode = false,
  editingTemplate,
  formData,
  validation,
  isSubmitting,
  onFieldChange,
  onCreate,
  onUpdate,
  onCancel,
}: CreateTemplatePopupProps) {
  const {
    primaryFolders,
    validation: treeValidation,
    isLoading: isTreeLoading,
    canDeleteFolder,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  } = useTemplateTreeViewWithData({
    templateId: editingTemplate?.id,
    isEditMode,
  });

  const isAnyFolderEditing = primaryFolders.some(
    (pf) => pf.isEditing || pf.secondaryFolders.some((sf) => sf.isEditing),
  );

  const isCreateDisabled =
    isSubmitting ||
    !formData.name.trim() ||
    !!validation.nameError ||
    !!treeValidation.duplicateNameError ||
    !!treeValidation.deleteLastSecondaryError ||
    isAnyFolderEditing;

  const handleSubmitClick = () => {
    logger.info("Form Data:", formData);
    logger.info("Folder Structure:", primaryFolders);
    if (isEditMode) {
      logger.info("Editing template:", editingTemplate as Record<string, any>);
      onUpdate?.();
    } else {
      onCreate();
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog
      onClose={onCancel}
      title={isEditMode ? "Edit Template" : "Create Template"}
      className="create-template-popup"
      width={950}
      height={450}
    >
      <div className="popup-content">
        <div className="popup-left">
          <CreateTemplateForm
            formData={formData}
            validation={validation}
            onFieldChange={onFieldChange}
          />
        </div>

        <div className="popup-right">
          <CreateTemplateTreeView
            primaryFolders={primaryFolders}
            validation={treeValidation}
            canDeleteFolder={canDeleteFolder}
            onAddPrimaryFolder={addPrimaryFolder}
            onAddSecondaryFolder={addSecondaryFolder}
            onEditFolder={editFolder}
            onSaveFolder={saveFolder}
            onCancelEdit={cancelEdit}
            onDeleteFolder={deleteFolder}
            onToggleExpand={toggleExpand}
          />
        </div>
      </div>

      <DialogActionsBar layout="end">
        <Button onClick={onCancel} disabled={isSubmitting} fillMode="flat">
          Cancel
        </Button>
        <Button
          themeColor="primary"
          onClick={handleSubmitClick}
          disabled={isCreateDisabled}
        >
          {isSubmitting ? (
            <Loader size="small" type="infinite-spinner" themeColor="primary" />
          ) : (
            isEditMode ? "Update" : "Create"
          )}
        </Button>
      </DialogActionsBar>
    </Dialog>
  );
}
